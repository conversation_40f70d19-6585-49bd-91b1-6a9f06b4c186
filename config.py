# Configuration file for DIY ETF

# Polygon API Configuration
POLYGON_API_KEY = "********************************"

# Shariah Compliance Rules
MAX_DEBT_EQUITY_RATIO = 0.30  # 33% maximum debt-to-equity
MAX_HARAM_REVENUE_PCT = 0.05  # 5% maximum revenue from haram activities

# Valuation Adjustment Rules
VALUATION_RULES = {
    "highly_undervalued": {"peg_threshold": 0.8, "multiplier": 1.4},
    "undervalued": {"peg_threshold": 1.2, "multiplier": 1.2},
    "fair_value": {"peg_threshold": 2.0, "multiplier": 1.0},
    "overvalued": {"peg_threshold": 3.0, "multiplier": 0.8},
    "highly_overvalued": {"peg_threshold": float('inf'), "multiplier": 0.6}
}

# Portfolio Constraints
MAX_STOCKS = 50
MIN_MARKET_CAP = 1e9  # $1B minimum market cap
MAX_SINGLE_POSITION = 0.10  # 10% maximum single position

# Known Halal Stocks (manually curated list)
# Note: This should be regularly updated based on Shariah compliance reviews
HALAL_STOCKS = {
    # Technology
    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'ORCL',
    'CRM', 'ADBE', 'NFLX', 'AMD', 'INTC', 'QCOM', 'TXN', 'INTU',
    'CSCO', 'IBM', 'UBER', 'SHOP', 'SQ', 'PYPL', 'ZOOM', 'DOCU',
    'OKTA', 'SNOW', 'PLTR', 'RBLX', 'COIN',
    
    # Semiconductors
    'TSM', 'ASML', 'AVGO', 'MRVL', 'KLAC', 'LRCX', 'AMAT',
    
    # Healthcare & Biotech
    'JNJ', 'PFE', 'UNH', 'ABBV', 'TMO', 'DHR', 'ABT', 'ISRG',
    'GILD', 'VRTX', 'REGN', 'BIIB', 'MRNA', 'BNTX',
    
    # Consumer & Retail
    'COST', 'HD', 'NKE', 'SBUX', 'MCD', 'DIS', 'NFLX',
    
    # Industrial & Materials
    'CAT', 'BA', 'GE', 'MMM', 'HON', 'UPS', 'FDX',
    
    # Note: Banks, insurance, and companies with significant interest-based revenue are excluded
    # Alcohol, gambling, pork, and other haram industries are excluded
}

# Haram Industries (to exclude)
HARAM_INDUSTRIES = {
    'banks', 'insurance', 'alcohol', 'gambling', 'tobacco', 'weapons',
    'pork', 'adult_entertainment', 'conventional_finance'
}

# Default Stock Universe (your manually selected stocks)
DEFAULT_STOCK_UNIVERSE = [
    "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META", "TSM",
    "ASML", "AVGO", "ORCL", "CRM", "ADBE", "NFLX", "AMD", "INTC",
    "QCOM", "TXN", "INTU", "CSCO", "IBM", "UBER", "SHOP", "SQ",
    "PYPL", "ZOOM", "DOCU", "OKTA", "SNOW", "PLTR", "RBLX", "COIN",
    "JNJ", "PFE", "UNH", "ABBV", "TMO", "DHR", "ABT", "ISRG",
    "COST", "HD", "NKE", "SBUX", "MCD", "DIS", "CAT", "BA", "GE"
]

# Rebalancing Settings
REBALANCE_FREQUENCY = "monthly"  # monthly, quarterly, semi-annually
MIN_REBALANCE_THRESHOLD = 0.05  # 5% drift before rebalancing

# Reporting Settings
CURRENCY = "EUR"
DECIMAL_PLACES = 2
