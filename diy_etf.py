import requests
import pandas as pd

API_KEY = "T590nL4IP66lUeIbYH8wDriLloGOcgxJ"
BASE_URL = "https://api.polygon.io"

# ----------------------
# Fetch fundamentals
# ----------------------
def get_fundamentals(ticker):
    url = f"{BASE_URL}/vX/reference/financials?ticker={ticker}&limit=1&apiKey={API_KEY}"
    r = requests.get(url)
    data = r.json()
    results = data.get("results", [])
    if not results:
        return None
    
    f = results[0]
    ratios = f.get("financials", {}).get("ratios", {})
    return {
        "ticker": ticker,
        "market_cap": f.get("market_cap"),
        "pe_ratio": ratios.get("pe_ratio"),
        "peg_ratio": ratios.get("peg_ratio"),
        "debt_equity": ratios.get("debt_to_equity")
    }

# ----------------------
# Shariah compliance filter
# ----------------------
def is_shariah_compliant(stock):
    if stock["debt_equity"] is not None and stock["debt_equity"] > 0.33:
        return False
    return True

# ----------------------
# Valuation adjustment
# ----------------------
def adjust_weight(base_weight, peg_ratio):
    if peg_ratio is None:
        return base_weight
    if peg_ratio < 1:
        return base_weight * 1.3  # boost undervalued
    elif peg_ratio > 2.5:
        return base_weight * 0.7  # cut overvalued
    else:
        return base_weight  # neutral

# ----------------------
# Portfolio allocation
# ----------------------
def build_portfolio(tickers, total_investment=1000):
    stocks_data = []
    
    # Fetch data
    for t in tickers:
        f = get_fundamentals(t)
        if f:
            stocks_data.append(f)
    
    df = pd.DataFrame(stocks_data).dropna(subset=["market_cap"])
    
    # Apply Shariah filter
    df = df[df.apply(is_shariah_compliant, axis=1)]
    
    if df.empty:
        print("No stocks passed Shariah filter.")
        return None
    
    # Base weights by market cap
    total_mc = df["market_cap"].sum()
    df["base_weight"] = df["market_cap"] / total_mc
    
    # Adjust weights by valuation
    df["adj_weight"] = df.apply(lambda x: adjust_weight(x["base_weight"], x["peg_ratio"]), axis=1)
    
    # Redistribute freed weight prioritizing undervalued
    undervalued = df[df["peg_ratio"] < 1]
    if not undervalued.empty:
        extra = 1 - df["adj_weight"].sum()
        if extra > 0:
            score_sum = sum((2 - undervalued["peg_ratio"]).clip(lower=0))
            for idx in undervalued.index:
                score = max(0, 2 - df.loc[idx, "peg_ratio"])
                df.loc[idx, "adj_weight"] += extra * (score / score_sum)
    
    # Normalize to 100%
    df["final_weight"] = df["adj_weight"] / df["adj_weight"].sum()
    
    # Allocation
    df["allocation"] = df["final_weight"] * total_investment
    
    return df[["ticker", "market_cap", "pe_ratio", "peg_ratio", "debt_equity", "final_weight", "allocation"]]

# ----------------------
# Example run
# ----------------------
if __name__ == "__main__":
    tickers = ["AAPL", "MSFT", "NVDA", "TSM", "GOOGL", "META"]  # up to 50
    portfolio = build_portfolio(tickers, total_investment=1000)
    if portfolio is not None:
        print(portfolio.to_string(index=False))
