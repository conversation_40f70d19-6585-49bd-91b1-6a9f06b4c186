import requests
import pandas as pd
import numpy as np
import sqlite3
import datetime
import time
import os
from typing import List, Dict, Optional
import matplotlib.pyplot as plt

API_KEY = "T590nL4IP66lUeIbYH8wDriLloGOcgxJ"
BASE_URL = "https://api.polygon.io"

# Create data directory if it doesn't exist
os.makedirs("data", exist_ok=True)

class DIYETFManager:
    def __init__(self, api_key: str = API_KEY):
        self.api_key = api_key
        self.base_url = BASE_URL
        self.db_path = "data/etf_data.db"
        self.init_database()

    def init_database(self):
        """Initialize SQLite database for storing historical data"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Table for fundamentals
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fundamentals (
                ticker TEXT,
                date TEXT,
                market_cap REAL,
                pe_ratio REAL,
                peg_ratio REAL,
                debt_equity REAL,
                revenue_growth REAL,
                is_halal INTEGER,
                PRIMARY KEY (ticker, date)
            )
        ''')

        # Table for prices
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS prices (
                ticker TEXT,
                date TEXT,
                close_price REAL,
                PRIMARY KEY (ticker, date)
            )
        ''')

        # Table for portfolio snapshots
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS portfolio_snapshots (
                date TEXT,
                ticker TEXT,
                weight REAL,
                allocation REAL,
                reason TEXT,
                PRIMARY KEY (date, ticker)
            )
        ''')

        conn.commit()
        conn.close()

    def get_fundamentals(self, ticker: str) -> Optional[Dict]:
        """Fetch fundamentals from Polygon API"""
        try:
            # Get ticker details for market cap
            url = f"{self.base_url}/v3/reference/tickers/{ticker}?apikey={self.api_key}"
            response = requests.get(url)
            ticker_data = response.json()

            market_cap = None
            if 'results' in ticker_data:
                market_cap = ticker_data['results'].get('market_cap')

            # Get financials
            url = f"{self.base_url}/vX/reference/financials?ticker={ticker}&limit=1&apikey={self.api_key}"
            response = requests.get(url)
            data = response.json()

            if 'results' not in data or not data['results']:
                return None

            financials = data['results'][0]

            # Extract key metrics
            balance_sheet = financials.get('financials', {}).get('balance_sheet', {})
            income_statement = financials.get('financials', {}).get('income_statement', {})

            # Calculate debt-to-equity (interest-bearing debt only for Shariah compliance)
            # Try to get specific debt items first
            short_term_debt = balance_sheet.get('current_debt', {}).get('value', 0) or \
                             balance_sheet.get('short_term_debt', {}).get('value', 0) or 0
            long_term_debt = balance_sheet.get('long_term_debt', {}).get('value', 0) or \
                            balance_sheet.get('noncurrent_debt', {}).get('value', 0) or 0

            # If specific debt items not available, use a conservative estimate
            # (typically 20-40% of total liabilities for tech companies)
            if short_term_debt == 0 and long_term_debt == 0:
                current_liab = balance_sheet.get('current_liabilities', {}).get('value', 0)
                noncurrent_liab = balance_sheet.get('noncurrent_liabilities', {}).get('value', 0)
                # Conservative estimate: assume 25% of liabilities are interest-bearing debt
                estimated_debt = (current_liab + noncurrent_liab) * 0.25
                total_debt = estimated_debt
            else:
                total_debt = short_term_debt + long_term_debt

            total_equity = balance_sheet.get('equity', {}).get('value', 1)
            debt_equity = total_debt / total_equity if total_equity > 0 else None

            # Get revenue for growth calculation
            revenue = income_statement.get('revenues', {}).get('value')

            return {
                "ticker": ticker,
                "market_cap": market_cap,
                "pe_ratio": None,  # Will calculate separately
                "peg_ratio": None,  # Will calculate separately
                "debt_equity": debt_equity,
                "revenue": revenue,
                "total_debt": total_debt,
                "total_equity": total_equity
            }

        except Exception as e:
            print(f"Error fetching fundamentals for {ticker}: {e}")
            return None

    def get_historical_prices(self, ticker: str, start_date: str, end_date: str) -> List[Dict]:
        """Fetch historical monthly prices"""
        try:
            url = f"{self.base_url}/v2/aggs/ticker/{ticker}/range/1/month/{start_date}/{end_date}?apikey={self.api_key}"
            response = requests.get(url)
            data = response.json()

            prices = []
            if 'results' in data:
                for item in data['results']:
                    date_str = datetime.datetime.fromtimestamp(item['t'] / 1000).strftime('%Y-%m-%d')
                    prices.append({
                        'ticker': ticker,
                        'date': date_str,
                        'close_price': item['c']
                    })
            return prices
        except Exception as e:
            print(f"Error fetching prices for {ticker}: {e}")
            return []

    def is_shariah_compliant(self, stock_data: Dict, debug: bool = False) -> bool:
        """Enhanced Shariah compliance check"""
        ticker = stock_data["ticker"]
        debt_equity = stock_data.get("debt_equity")

        # Import the updated threshold from config
        from config import MAX_DEBT_EQUITY_RATIO

        # Known halal stocks (manually curated)
        halal_tickers = {
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'TSM',
            'ASML', 'AVGO', 'ORCL', 'CRM', 'ADBE', 'NFLX', 'AMD', 'INTC',
            'QCOM', 'TXN', 'INTU', 'CSCO', 'IBM', 'UBER', 'SHOP', 'SQ',
            'PYPL', 'ZOOM', 'DOCU', 'OKTA', 'SNOW', 'PLTR', 'RBLX', 'COIN'
        }

        # Check if ticker is in halal list
        in_halal_list = ticker in halal_tickers

        # Check debt-to-equity ratio
        debt_ok = True
        if debt_equity is not None:
            debt_ok = debt_equity <= MAX_DEBT_EQUITY_RATIO

        if debug:
            debt_str = f"{debt_equity:.3f}" if debt_equity is not None else "N/A"
            print(f"  {ticker}: Halal list={in_halal_list}, D/E={debt_str}, "
                  f"D/E OK={debt_ok}, Final={in_halal_list and debt_ok}")

        return in_halal_list and debt_ok

    def calculate_valuation_score(self, stock_data: Dict) -> float:
        """Calculate valuation score for weight adjustment"""
        pe_ratio = stock_data.get('pe_ratio', 20)  # Default PE if missing
        peg_ratio = stock_data.get('peg_ratio', 1.5)  # Default PEG if missing

        # Simple valuation scoring
        if peg_ratio is None or peg_ratio <= 0:
            peg_ratio = 1.5

        if peg_ratio < 0.8:
            return 1.4  # Highly undervalued - boost significantly
        elif peg_ratio < 1.2:
            return 1.2  # Undervalued - boost moderately
        elif peg_ratio < 2.0:
            return 1.0  # Fair value - neutral
        elif peg_ratio < 3.0:
            return 0.8  # Overvalued - reduce
        else:
            return 0.6  # Highly overvalued - reduce significantly

    def build_portfolio(self, tickers: List[str], total_investment: float = 1000,
                       date: str = None) -> Optional[pd.DataFrame]:
        """Build portfolio allocation with enhanced logic"""
        if date is None:
            date = datetime.datetime.now().strftime('%Y-%m-%d')

        stocks_data = []

        print(f"Fetching data for {len(tickers)} stocks...")
        for ticker in tickers:
            print(f"Processing {ticker}...")
            fundamentals = self.get_fundamentals(ticker)
            if fundamentals:
                stocks_data.append(fundamentals)
            time.sleep(0.2)  # Rate limiting

        if not stocks_data:
            print("No valid stock data found.")
            return None

        df = pd.DataFrame(stocks_data)

        # Filter out stocks without market cap
        df = df.dropna(subset=['market_cap'])
        df = df[df['market_cap'] > 0]

        if df.empty:
            print("No stocks with valid market cap data.")
            return None

        # Apply Shariah compliance filter with debug output
        print("\nShariah Compliance Check:")
        df['is_halal'] = df.apply(lambda row: self.is_shariah_compliant(row, debug=True), axis=1)
        halal_df = df[df['is_halal']].copy()

        if halal_df.empty:
            print("No stocks passed Shariah compliance filter.")
            return None

        print(f"\n{len(halal_df)} stocks passed Shariah filter out of {len(df)} total.")

        # Calculate base weights by market cap
        total_market_cap = halal_df['market_cap'].sum()
        halal_df['base_weight'] = halal_df['market_cap'] / total_market_cap

        # Calculate valuation adjustments
        halal_df['valuation_multiplier'] = halal_df.apply(self.calculate_valuation_score, axis=1)
        halal_df['adjusted_weight'] = halal_df['base_weight'] * halal_df['valuation_multiplier']

        # Redistribute excess weight to undervalued stocks
        total_adjusted = halal_df['adjusted_weight'].sum()
        if total_adjusted != 1.0:
            # Find undervalued stocks (multiplier > 1.0)
            undervalued = halal_df[halal_df['valuation_multiplier'] > 1.0]

            if not undervalued.empty and total_adjusted < 1.0:
                # Distribute excess proportionally to undervalued stocks
                excess = 1.0 - total_adjusted
                undervalued_weights = undervalued['adjusted_weight'].sum()

                for idx in undervalued.index:
                    proportion = halal_df.loc[idx, 'adjusted_weight'] / undervalued_weights
                    halal_df.loc[idx, 'adjusted_weight'] += excess * proportion

        # Final normalization
        halal_df['final_weight'] = halal_df['adjusted_weight'] / halal_df['adjusted_weight'].sum()

        # Calculate allocations
        halal_df['allocation_eur'] = halal_df['final_weight'] * total_investment

        # Store snapshot in database
        self.store_portfolio_snapshot(halal_df, date)

        return halal_df[['ticker', 'market_cap', 'debt_equity', 'base_weight',
                        'valuation_multiplier', 'final_weight', 'allocation_eur']]

    def store_portfolio_snapshot(self, portfolio_df: pd.DataFrame, date: str):
        """Store portfolio snapshot in database"""
        conn = sqlite3.connect(self.db_path)

        for _, row in portfolio_df.iterrows():
            conn.execute('''
                INSERT OR REPLACE INTO portfolio_snapshots
                (date, ticker, weight, allocation, reason)
                VALUES (?, ?, ?, ?, ?)
            ''', (date, row['ticker'], row['final_weight'],
                 row['allocation_eur'], f"Valuation multiplier: {row['valuation_multiplier']:.2f}"))

        conn.commit()
        conn.close()

    def get_portfolio_history(self) -> pd.DataFrame:
        """Get historical portfolio snapshots"""
        conn = sqlite3.connect(self.db_path)
        df = pd.read_sql_query('''
            SELECT date, ticker, weight, allocation, reason
            FROM portfolio_snapshots
            ORDER BY date, ticker
        ''', conn)
        conn.close()
        return df

    def backtest_portfolio(self, tickers: List[str], start_date: str, end_date: str,
                          initial_investment: float = 1000) -> Dict:
        """Backtest the portfolio strategy"""
        print(f"Backtesting from {start_date} to {end_date}...")

        # Get historical prices for all tickers
        all_prices = {}
        for ticker in tickers:
            prices = self.get_historical_prices(ticker, start_date, end_date)
            if prices:
                price_df = pd.DataFrame(prices)
                price_df['date'] = pd.to_datetime(price_df['date'])
                price_df = price_df.set_index('date')
                all_prices[ticker] = price_df['close_price']

        if not all_prices:
            print("No price data available for backtesting.")
            return {}

        # Create monthly rebalancing dates
        start = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        rebalance_dates = pd.date_range(start=start, end=end, freq='MS')  # Month start

        portfolio_values = []
        current_value = initial_investment

        for date in rebalance_dates:
            date_str = date.strftime('%Y-%m-%d')
            print(f"Rebalancing on {date_str}...")

            # Build portfolio for this date (simplified - using current fundamentals)
            portfolio = self.build_portfolio(tickers, current_value, date_str)

            if portfolio is not None:
                # Calculate portfolio performance until next rebalance
                next_date = date + pd.DateOffset(months=1)
                if next_date > end:
                    next_date = end

                # Get price changes for each stock
                portfolio_return = 0
                for _, row in portfolio.iterrows():
                    ticker = row['ticker']
                    weight = row['final_weight']

                    if ticker in all_prices:
                        try:
                            start_price = all_prices[ticker].asof(date)
                            end_price = all_prices[ticker].asof(next_date)

                            if pd.notna(start_price) and pd.notna(end_price) and start_price > 0:
                                stock_return = (end_price - start_price) / start_price
                                portfolio_return += weight * stock_return
                        except:
                            continue

                current_value *= (1 + portfolio_return)
                portfolio_values.append({
                    'date': date_str,
                    'portfolio_value': current_value,
                    'return': portfolio_return
                })

        return {
            'portfolio_values': portfolio_values,
            'total_return': (current_value - initial_investment) / initial_investment,
            'final_value': current_value
        }

    def generate_report(self, portfolio: pd.DataFrame, total_investment: float):
        """Generate a comprehensive portfolio report"""
        print("\n" + "="*80)
        print("DIY SHARIAH-COMPLIANT ETF ALLOCATION REPORT")
        print("="*80)
        print(f"Total Investment: €{total_investment:,.2f}")
        print(f"Number of Holdings: {len(portfolio)}")
        print(f"Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n" + "-"*80)

        # Sort by allocation descending
        portfolio_sorted = portfolio.sort_values('allocation_eur', ascending=False)

        print(f"{'Ticker':<8} {'Market Cap':<12} {'Base %':<8} {'Val Mult':<9} {'Final %':<8} {'Amount €':<10}")
        print("-"*80)

        for _, row in portfolio_sorted.iterrows():
            print(f"{row['ticker']:<8} "
                  f"€{row['market_cap']/1e9:>8.1f}B "
                  f"{row['base_weight']*100:>6.1f}% "
                  f"{row['valuation_multiplier']:>7.2f}x "
                  f"{row['final_weight']*100:>6.1f}% "
                  f"€{row['allocation_eur']:>8.0f}")

        print("-"*80)
        print(f"{'TOTAL':<8} {'':>12} {'100.0%':<8} {'':>9} {'100.0%':<8} €{total_investment:>8.0f}")

        # Summary statistics
        print(f"\nTop 5 Holdings: {portfolio_sorted['final_weight'].head(5).sum()*100:.1f}%")
        print(f"Average Allocation: €{portfolio['allocation_eur'].mean():.0f}")
        max_weight_idx = portfolio['final_weight'].idxmax()
        largest_ticker = portfolio.loc[max_weight_idx, 'ticker']
        largest_weight = portfolio['final_weight'].max()
        print(f"Largest Position: {largest_ticker} ({largest_weight*100:.1f}%)")

# ----------------------
# Example usage
# ----------------------
def main():
    # Initialize the ETF manager
    etf = DIYETFManager()

    # Define your stock universe (up to 50 stocks)
    my_stocks = [
        "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META", "TSM",
        "ASML", "AVGO", "ORCL", "CRM", "ADBE", "NFLX", "AMD", "INTC",
        "QCOM", "TXN", "INTU", "CSCO", "IBM", "UBER", "SHOP", "SQ"
    ]

    # Build current portfolio
    print("Building current portfolio allocation...")
    portfolio = etf.build_portfolio(my_stocks, total_investment=1000)

    if portfolio is not None:
        etf.generate_report(portfolio, 1000)

        # Save to CSV for further analysis
        portfolio.to_csv('data/current_portfolio.csv', index=False)
        print(f"\nPortfolio saved to data/current_portfolio.csv")

        # Optional: Run backtest
        print("\nWould you like to run a backtest? (This may take a while)")
        # backtest_results = etf.backtest_portfolio(my_stocks[:10], "2020-01-01", "2024-01-01")
        # print(f"Backtest results: {backtest_results}")

if __name__ == "__main__":
    main()
