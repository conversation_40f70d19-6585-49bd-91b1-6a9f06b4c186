#!/usr/bin/env python3
"""
Global DIY Shariah-Compliant ETF using Zoya API
Fetches top 100 global companies and filters using professional Shariah compliance data
"""

import requests
import pandas as pd
import numpy as np
import sqlite3
import datetime
import time
import os
from typing import List, Dict, Optional
import json

# Configuration
POLYGON_API_KEY = "********************************"
ZOYA_API_ENDPOINT = "https://api.zoya.finance/graphql"
ZOYA_SANDBOX_ENDPOINT = "https://sandbox-api.zoya.finance/graphql"
ZOYA_API_KEY = "sandbox-3b0ba3bb-fa32-4d0d-bc94-3bc5052c36de"  # Sandbox key

# Create data directory
os.makedirs("data", exist_ok=True)

class GlobalHalalETF:
    def __init__(self):
        self.polygon_key = POLYGON_API_KEY
        self.zoya_endpoint = ZOYA_SANDBOX_ENDPOINT  # Start with sandbox
        self.zoya_key = ZOYA_API_KEY
        self.db_path = "data/global_etf_data.db"
        self.init_database()
    
    def init_database(self):
        """Initialize database for global ETF data"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS global_stocks (
                ticker TEXT PRIMARY KEY,
                company_name TEXT,
                country TEXT,
                market_cap REAL,
                sector TEXT,
                is_halal INTEGER,
                zoya_score TEXT,
                last_updated TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS portfolio_allocations (
                date TEXT,
                ticker TEXT,
                weight REAL,
                allocation_eur REAL,
                reason TEXT,
                PRIMARY KEY (date, ticker)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def get_top_global_companies(self) -> List[Dict]:
        """Get top 100 global companies by market cap"""
        # For now, we'll use a curated list of top global companies
        # In production, you'd fetch this from a financial data provider
        
        top_global_companies = [
            # US Tech Giants
            "AAPL", "MSFT", "GOOGL", "AMZN", "NVDA", "META", "TSLA", "ORCL", "CRM", "ADBE",
            "NFLX", "AMD", "INTC", "QCOM", "CSCO", "IBM", "UBER", "SHOP", "SQ", "PYPL",
            
            # US Other Sectors
            "BRK.A", "UNH", "JNJ", "JPM", "V", "PG", "HD", "MA", "BAC", "ABBV",
            "KO", "PFE", "AVGO", "TMO", "COST", "DIS", "ABT", "VZ", "ADBE", "NKE",
            
            # European Companies
            "ASML", "SAP", "LVMH", "NVO", "NESN", "ROCHE", "TM", "SHEL", "AZN", "RDSA",
            
            # Asian Companies (ADRs and direct listings)
            "TSM", "BABA", "TCEHY", "NVS", "UL", "SNY", "DEO", "BUD", "NMR", "ING",
            
            # Additional Global Leaders
            "MC.PA", "OR.PA", "CDI.PA", "AIR.PA", "SAN.PA", "BNP.PA", "TTE.PA",
            "VOW3.DE", "SAP.DE", "SIE.DE", "ALV.DE", "DTE.DE", "BAS.DE",
            "7203.T", "6758.T", "9984.T", "6861.T", "8306.T", "9432.T",
            "0700.HK", "0941.HK", "1299.HK", "2318.HK", "0005.HK", "3690.HK"
        ]
        
        return [{"ticker": ticker} for ticker in top_global_companies[:100]]
    
    def get_shariah_compliance_data(self) -> Dict:
        """Get comprehensive Shariah compliance database"""

        # Comprehensive halal stocks database (manually curated)
        halal_stocks = {
            # US Technology - Generally halal if debt ratios acceptable
            'AAPL': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Low debt, tech business'},
            'MSFT': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Software/cloud business'},
            'GOOGL': {'confidence': 'medium', 'sector': 'Technology', 'notes': 'Ad revenue concerns'},
            'AMZN': {'confidence': 'medium', 'sector': 'E-commerce', 'notes': 'Some financing activities'},
            'NVDA': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Hardware/AI business'},
            'META': {'confidence': 'medium', 'sector': 'Technology', 'notes': 'Ad revenue model'},
            'TSLA': {'confidence': 'high', 'sector': 'Automotive', 'notes': 'Electric vehicles'},
            'NFLX': {'confidence': 'high', 'sector': 'Media', 'notes': 'Streaming service'},
            'AMD': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Semiconductor business'},
            'INTC': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Semiconductor business'},
            'ORCL': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Database software'},
            'CRM': {'confidence': 'high', 'sector': 'Technology', 'notes': 'SaaS business'},
            'ADBE': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Software business'},
            'SHOP': {'confidence': 'high', 'sector': 'Technology', 'notes': 'E-commerce platform'},
            'SQ': {'confidence': 'medium', 'sector': 'Technology', 'notes': 'Some financial services'},
            'UBER': {'confidence': 'medium', 'sector': 'Technology', 'notes': 'Transportation platform'},
            'ZOOM': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Video conferencing'},

            # International Technology
            'TSM': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Semiconductor manufacturing'},
            'ASML': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Semiconductor equipment'},
            'SAP': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Enterprise software'},

            # Healthcare & Pharmaceuticals
            'JNJ': {'confidence': 'medium', 'sector': 'Healthcare', 'notes': 'Pharmaceutical/medical devices'},
            'PFE': {'confidence': 'medium', 'sector': 'Healthcare', 'notes': 'Pharmaceutical'},
            'ABBV': {'confidence': 'medium', 'sector': 'Healthcare', 'notes': 'Pharmaceutical'},
            'TMO': {'confidence': 'high', 'sector': 'Healthcare', 'notes': 'Life sciences equipment'},
            'DHR': {'confidence': 'high', 'sector': 'Healthcare', 'notes': 'Medical technology'},
            'ABT': {'confidence': 'medium', 'sector': 'Healthcare', 'notes': 'Medical devices'},

            # Consumer Goods
            'PG': {'confidence': 'high', 'sector': 'Consumer Goods', 'notes': 'Consumer products'},
            'KO': {'confidence': 'high', 'sector': 'Beverages', 'notes': 'Non-alcoholic beverages'},
            'NKE': {'confidence': 'high', 'sector': 'Consumer Goods', 'notes': 'Athletic footwear'},
            'HD': {'confidence': 'high', 'sector': 'Retail', 'notes': 'Home improvement retail'},
            'COST': {'confidence': 'high', 'sector': 'Retail', 'notes': 'Warehouse retail'},
            'SBUX': {'confidence': 'high', 'sector': 'Food & Beverage', 'notes': 'Coffee chain'},
            'MCD': {'confidence': 'high', 'sector': 'Food & Beverage', 'notes': 'Fast food chain'},

            # Industrial
            'CAT': {'confidence': 'medium', 'sector': 'Industrial', 'notes': 'Heavy machinery'},
            'BA': {'confidence': 'low', 'sector': 'Aerospace', 'notes': 'Defense contracts concern'},
            'GE': {'confidence': 'medium', 'sector': 'Industrial', 'notes': 'Diversified industrial'},
            'MMM': {'confidence': 'high', 'sector': 'Industrial', 'notes': 'Industrial products'},
            'HON': {'confidence': 'medium', 'sector': 'Industrial', 'notes': 'Aerospace/defense exposure'},

            # International Consumer/Industrial
            'NESN': {'confidence': 'high', 'sector': 'Food & Beverage', 'notes': 'Food products'},
            'UL': {'confidence': 'high', 'sector': 'Consumer Goods', 'notes': 'Consumer products'},
            'LVMH': {'confidence': 'medium', 'sector': 'Luxury Goods', 'notes': 'Luxury products'},
        }

        # Definitely haram stocks
        haram_stocks = {
            # Banking & Financial Services
            'JPM': 'Interest-based banking',
            'BAC': 'Interest-based banking',
            'WFC': 'Interest-based banking',
            'GS': 'Investment banking',
            'MS': 'Investment banking',
            'C': 'Interest-based banking',
            'USB': 'Interest-based banking',
            'PNC': 'Interest-based banking',

            # Insurance
            'BRK.A': 'Insurance/banking activities',
            'BRK.B': 'Insurance/banking activities',
            'AIG': 'Insurance',
            'PRU': 'Insurance',
            'MET': 'Insurance',

            # Payment Processing (interest-based)
            'V': 'Interest-based payment processing',
            'MA': 'Interest-based payment processing',
            'AXP': 'Credit/financial services',

            # Alcohol & Tobacco
            'BUD': 'Alcohol production',
            'TAP': 'Alcohol production',
            'DEO': 'Alcohol production',
            'MO': 'Tobacco',
            'PM': 'Tobacco',
            'BTI': 'Tobacco',

            # Gambling & Entertainment
            'LVS': 'Casino/gambling',
            'MGM': 'Casino/gambling',
            'WYNN': 'Casino/gambling',
            'CZR': 'Casino/gambling',

            # Defense/Weapons
            'LMT': 'Defense contractor',
            'RTX': 'Defense contractor',
            'NOC': 'Defense contractor',
            'GD': 'Defense contractor',
        }

        return {'halal': halal_stocks, 'haram': haram_stocks}
    
    def get_polygon_data(self, ticker: str) -> Optional[Dict]:
        """Get market cap and financial data from Polygon"""
        try:
            # Get ticker details
            url = f"https://api.polygon.io/v3/reference/tickers/{ticker}?apikey={self.polygon_key}"
            response = requests.get(url)
            
            if response.status_code != 200:
                return None
                
            data = response.json()
            if 'results' not in data:
                return None
            
            results = data['results']
            return {
                "ticker": ticker,
                "market_cap": results.get('market_cap'),
                "name": results.get('name'),
                "locale": results.get('locale', 'US'),
                "type": results.get('type')
            }
            
        except Exception as e:
            print(f"Error fetching Polygon data for {ticker}: {e}")
            return None
    
    def check_shariah_compliance(self, ticker: str, market_data: Dict) -> Dict:
        """Check if a stock is Shariah compliant using multiple criteria"""

        compliance_db = self.get_shariah_compliance_data()
        halal_stocks = compliance_db['halal']
        haram_stocks = compliance_db['haram']

        # Check if explicitly haram
        if ticker in haram_stocks:
            return {
                'is_halal': False,
                'confidence': 'high',
                'reason': haram_stocks[ticker],
                'source': 'manual_database'
            }

        # Check if explicitly halal
        if ticker in halal_stocks:
            stock_info = halal_stocks[ticker]
            return {
                'is_halal': True,
                'confidence': stock_info['confidence'],
                'reason': stock_info['notes'],
                'source': 'manual_database',
                'sector': stock_info['sector']
            }

        # For unknown stocks, apply basic screening
        # This is a simplified approach - in practice you'd need more detailed analysis
        return {
            'is_halal': False,  # Conservative approach for unknown stocks
            'confidence': 'low',
            'reason': 'Not in verified halal database',
            'source': 'conservative_screening'
        }

    def build_global_halal_portfolio(self, investment_amount: float = 1000) -> Optional[pd.DataFrame]:
        """Build portfolio from top global halal companies"""

        print("🌍 Building Global Halal ETF Portfolio...")
        print("=" * 60)

        # Step 1: Get top global companies
        print("1. Fetching top 100 global companies...")
        companies = self.get_top_global_companies()
        tickers = [c["ticker"] for c in companies]

        # Step 2: Get market cap data from Polygon
        print("2. Fetching market cap and fundamental data...")
        market_data = {}

        for i, ticker in enumerate(tickers[:50]):  # Limit to 50 for testing
            print(f"   Fetching {ticker} ({i+1}/50)...")
            polygon_data = self.get_polygon_data(ticker)
            if polygon_data and polygon_data.get('market_cap'):
                market_data[ticker] = polygon_data
            time.sleep(0.2)  # Rate limiting

        # Step 3: Apply Shariah compliance screening
        print("3. Applying Shariah compliance screening...")

        portfolio_data = []
        halal_count = 0
        haram_count = 0

        for ticker, market_info in market_data.items():
            compliance_result = self.check_shariah_compliance(ticker, market_info)

            print(f"   {ticker}: {'✅ HALAL' if compliance_result['is_halal'] else '❌ HARAM'} "
                  f"({compliance_result['confidence']} confidence) - {compliance_result['reason']}")

            if compliance_result['is_halal']:
                halal_count += 1
                portfolio_data.append({
                    'ticker': ticker,
                    'company_name': market_info.get('name', ''),
                    'market_cap': market_info['market_cap'],
                    'country': market_info.get('locale', 'US'),
                    'sector': compliance_result.get('sector', 'Unknown'),
                    'confidence': compliance_result['confidence'],
                    'halal_reason': compliance_result['reason'],
                    'compliance_source': compliance_result['source']
                })
            else:
                haram_count += 1

        print(f"\n📊 Screening Results:")
        print(f"   ✅ Halal stocks: {halal_count}")
        print(f"   ❌ Haram stocks: {haram_count}")
        print(f"   📈 Portfolio stocks: {len(portfolio_data)}")

        if not portfolio_data:
            print("❌ No halal stocks found with valid market cap data")
            return None

        # Step 4: Create portfolio allocation with valuation adjustments
        df = pd.DataFrame(portfolio_data)

        # Calculate base market cap weights
        total_market_cap = df['market_cap'].sum()
        df['base_weight'] = df['market_cap'] / total_market_cap

        # Apply confidence-based adjustments
        # Higher confidence stocks get slight boost, lower confidence get slight reduction
        df['confidence_multiplier'] = df['confidence'].map({
            'high': 1.1,
            'medium': 1.0,
            'low': 0.9
        })

        df['adjusted_weight'] = df['base_weight'] * df['confidence_multiplier']

        # Normalize to 100%
        df['final_weight'] = df['adjusted_weight'] / df['adjusted_weight'].sum()
        df['allocation_eur'] = df['final_weight'] * investment_amount

        # Sort by allocation
        df = df.sort_values('allocation_eur', ascending=False)

        return df
    
    def generate_global_report(self, portfolio: pd.DataFrame, investment_amount: float):
        """Generate comprehensive global portfolio report"""

        print("\n" + "=" * 80)
        print("🌍 GLOBAL HALAL ETF ALLOCATION REPORT")
        print("=" * 80)
        print(f"Total Investment: €{investment_amount:,.2f}")
        print(f"Number of Holdings: {len(portfolio)}")
        print(f"Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Confidence distribution
        print(f"\n🔍 Confidence Distribution:")
        conf_dist = portfolio.groupby('confidence')['final_weight'].sum().sort_values(ascending=False)
        for confidence, weight in conf_dist.items():
            print(f"  {confidence.title()} confidence: {weight*100:.1f}%")

        # Sector distribution
        print(f"\n🏢 Sector Distribution:")
        sector_dist = portfolio.groupby('sector')['final_weight'].sum().sort_values(ascending=False)
        for sector, weight in sector_dist.items():
            print(f"  {sector}: {weight*100:.1f}%")

        # Geographic distribution
        print(f"\n📍 Geographic Distribution:")
        geo_dist = portfolio.groupby('country')['final_weight'].sum().sort_values(ascending=False)
        for country, weight in geo_dist.items():
            print(f"  {country}: {weight*100:.1f}%")

        print("\n" + "-" * 95)
        print(f"{'Ticker':<8} {'Company':<20} {'Sector':<12} {'Conf':<6} {'Weight':<8} {'Amount €':<10}")
        print("-" * 95)

        for _, row in portfolio.head(20).iterrows():  # Top 20 holdings
            company_short = row['company_name'][:18] + ".." if len(row['company_name']) > 20 else row['company_name']
            sector_short = row['sector'][:10] + ".." if len(row['sector']) > 12 else row['sector']
            print(f"{row['ticker']:<8} {company_short:<20} {sector_short:<12} "
                  f"{row['confidence']:<6} {row['final_weight']*100:>6.1f}% €{row['allocation_eur']:>8.0f}")

        if len(portfolio) > 20:
            others_weight = portfolio.iloc[20:]['final_weight'].sum()
            others_amount = portfolio.iloc[20:]['allocation_eur'].sum()
            print(f"{'Others':<8} {'('+str(len(portfolio)-20)+' more stocks)':<20} {'Mixed':<12} "
                  f"{'Mixed':<6} {others_weight*100:>6.1f}% €{others_amount:>8.0f}")

        print("-" * 95)
        print(f"{'TOTAL':<47} {'100.0%':<8} €{investment_amount:>8.0f}")

        # Summary statistics
        print(f"\n📈 Portfolio Statistics:")
        print(f"  Average allocation: €{portfolio['allocation_eur'].mean():.0f}")
        print(f"  Largest position: {portfolio.iloc[0]['ticker']} ({portfolio.iloc[0]['final_weight']*100:.1f}%)")
        print(f"  Top 5 concentration: {portfolio.head(5)['final_weight'].sum()*100:.1f}%")
        print(f"  High confidence stocks: {len(portfolio[portfolio['confidence'] == 'high'])}")
        print(f"  Technology exposure: {portfolio[portfolio['sector'] == 'Technology']['final_weight'].sum()*100:.1f}%")

def main():
    """Main execution function"""
    etf = GlobalHalalETF()
    
    # Build global halal portfolio
    portfolio = etf.build_global_halal_portfolio(investment_amount=1000)
    
    if portfolio is not None:
        etf.generate_global_report(portfolio, 1000)
        
        # Save results
        portfolio.to_csv('data/global_halal_portfolio.csv', index=False)
        print(f"\n💾 Portfolio saved to: data/global_halal_portfolio.csv")
    else:
        print("❌ Failed to build portfolio")

if __name__ == "__main__":
    main()
