#!/usr/bin/env python3
"""
Global DIY Shariah-Compliant ETF using Zoya API
Fetches top 100 global companies and filters using professional Shariah compliance data
"""

import requests
import pandas as pd
import numpy as np
import sqlite3
import datetime
import time
import os
from typing import List, Dict, Optional
import json

# Configuration
POLYGON_API_KEY = "********************************"
ZOYA_API_ENDPOINT = "https://api.zoya.finance/graphql"
ZOYA_SANDBOX_ENDPOINT = "https://sandbox-api.zoya.finance/graphql"
ZOYA_API_KEY = "sandbox-3b0ba3bb-fa32-4d0d-bc94-3bc5052c36de"  # Sandbox key

# Create data directory
os.makedirs("data", exist_ok=True)

class GlobalHalalETF:
    def __init__(self):
        self.polygon_key = POLYGON_API_KEY
        self.zoya_endpoint = ZOYA_SANDBOX_ENDPOINT  # Start with sandbox
        self.zoya_key = ZOYA_API_KEY
        self.db_path = "data/global_etf_data.db"
        self.init_database()
    
    def init_database(self):
        """Initialize database for global ETF data"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS global_stocks (
                ticker TEXT PRIMARY KEY,
                company_name TEXT,
                country TEXT,
                market_cap REAL,
                sector TEXT,
                is_halal INTEGER,
                zoya_score TEXT,
                last_updated TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS portfolio_allocations (
                date TEXT,
                ticker TEXT,
                weight REAL,
                allocation_eur REAL,
                reason TEXT,
                PRIMARY KEY (date, ticker)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def get_top_global_companies(self) -> List[Dict]:
        """Get top 100 global companies by market cap"""
        # For now, we'll use a curated list of top global companies
        # In production, you'd fetch this from a financial data provider
        
        top_global_companies = [
            # US Tech Giants
            "AAPL", "MSFT", "GOOGL", "AMZN", "NVDA", "META", "TSLA", "ORCL", "CRM", "ADBE",
            "NFLX", "AMD", "INTC", "QCOM", "CSCO", "IBM", "UBER", "SHOP", "SQ", "PYPL",
            
            # US Other Sectors
            "BRK.A", "UNH", "JNJ", "JPM", "V", "PG", "HD", "MA", "BAC", "ABBV",
            "KO", "PFE", "AVGO", "TMO", "COST", "DIS", "ABT", "VZ", "ADBE", "NKE",
            
            # European Companies
            "ASML", "SAP", "LVMH", "NVO", "NESN", "ROCHE", "TM", "SHEL", "AZN", "RDSA",
            
            # Asian Companies (ADRs and direct listings)
            "TSM", "BABA", "TCEHY", "NVS", "UL", "SNY", "DEO", "BUD", "NMR", "ING",
            
            # Additional Global Leaders
            "MC.PA", "OR.PA", "CDI.PA", "AIR.PA", "SAN.PA", "BNP.PA", "TTE.PA",
            "VOW3.DE", "SAP.DE", "SIE.DE", "ALV.DE", "DTE.DE", "BAS.DE",
            "7203.T", "6758.T", "9984.T", "6861.T", "8306.T", "9432.T",
            "0700.HK", "0941.HK", "1299.HK", "2318.HK", "0005.HK", "3690.HK"
        ]
        
        return [{"ticker": ticker} for ticker in top_global_companies[:100]]
    
    def get_shariah_compliance_data(self) -> Dict:
        """Get comprehensive Shariah compliance database"""

        # Comprehensive halal stocks database (manually curated)
        halal_stocks = {
            # US Technology - Generally halal if debt ratios acceptable
            'AAPL': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Low debt, tech business'},
            'MSFT': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Software/cloud business'},
            'GOOGL': {'confidence': 'medium', 'sector': 'Technology', 'notes': 'Ad revenue concerns'},
            'AMZN': {'confidence': 'medium', 'sector': 'E-commerce', 'notes': 'Some financing activities'},
            'NVDA': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Hardware/AI business'},
            'META': {'confidence': 'medium', 'sector': 'Technology', 'notes': 'Ad revenue model'},
            'TSLA': {'confidence': 'high', 'sector': 'Automotive', 'notes': 'Electric vehicles'},
            'NFLX': {'confidence': 'high', 'sector': 'Media', 'notes': 'Streaming service'},
            'AMD': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Semiconductor business'},
            'INTC': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Semiconductor business'},
            'ORCL': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Database software'},
            'CRM': {'confidence': 'high', 'sector': 'Technology', 'notes': 'SaaS business'},
            'ADBE': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Software business'},
            'SHOP': {'confidence': 'high', 'sector': 'Technology', 'notes': 'E-commerce platform'},
            'SQ': {'confidence': 'medium', 'sector': 'Technology', 'notes': 'Some financial services'},
            'UBER': {'confidence': 'medium', 'sector': 'Technology', 'notes': 'Transportation platform'},
            'ZOOM': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Video conferencing'},

            # International Technology
            'TSM': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Semiconductor manufacturing'},
            'ASML': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Semiconductor equipment'},
            'SAP': {'confidence': 'high', 'sector': 'Technology', 'notes': 'Enterprise software'},

            # Healthcare & Pharmaceuticals
            'JNJ': {'confidence': 'medium', 'sector': 'Healthcare', 'notes': 'Pharmaceutical/medical devices'},
            'PFE': {'confidence': 'medium', 'sector': 'Healthcare', 'notes': 'Pharmaceutical'},
            'ABBV': {'confidence': 'medium', 'sector': 'Healthcare', 'notes': 'Pharmaceutical'},
            'TMO': {'confidence': 'high', 'sector': 'Healthcare', 'notes': 'Life sciences equipment'},
            'DHR': {'confidence': 'high', 'sector': 'Healthcare', 'notes': 'Medical technology'},
            'ABT': {'confidence': 'medium', 'sector': 'Healthcare', 'notes': 'Medical devices'},

            # Consumer Goods
            'PG': {'confidence': 'high', 'sector': 'Consumer Goods', 'notes': 'Consumer products'},
            'KO': {'confidence': 'high', 'sector': 'Beverages', 'notes': 'Non-alcoholic beverages'},
            'NKE': {'confidence': 'high', 'sector': 'Consumer Goods', 'notes': 'Athletic footwear'},
            'HD': {'confidence': 'high', 'sector': 'Retail', 'notes': 'Home improvement retail'},
            'COST': {'confidence': 'high', 'sector': 'Retail', 'notes': 'Warehouse retail'},
            'SBUX': {'confidence': 'high', 'sector': 'Food & Beverage', 'notes': 'Coffee chain'},
            'MCD': {'confidence': 'high', 'sector': 'Food & Beverage', 'notes': 'Fast food chain'},

            # Industrial
            'CAT': {'confidence': 'medium', 'sector': 'Industrial', 'notes': 'Heavy machinery'},
            'BA': {'confidence': 'low', 'sector': 'Aerospace', 'notes': 'Defense contracts concern'},
            'GE': {'confidence': 'medium', 'sector': 'Industrial', 'notes': 'Diversified industrial'},
            'MMM': {'confidence': 'high', 'sector': 'Industrial', 'notes': 'Industrial products'},
            'HON': {'confidence': 'medium', 'sector': 'Industrial', 'notes': 'Aerospace/defense exposure'},

            # International Consumer/Industrial
            'NESN': {'confidence': 'high', 'sector': 'Food & Beverage', 'notes': 'Food products'},
            'UL': {'confidence': 'high', 'sector': 'Consumer Goods', 'notes': 'Consumer products'},
            'LVMH': {'confidence': 'medium', 'sector': 'Luxury Goods', 'notes': 'Luxury products'},
        }

        # Definitely haram stocks
        haram_stocks = {
            # Banking & Financial Services
            'JPM': 'Interest-based banking',
            'BAC': 'Interest-based banking',
            'WFC': 'Interest-based banking',
            'GS': 'Investment banking',
            'MS': 'Investment banking',
            'C': 'Interest-based banking',
            'USB': 'Interest-based banking',
            'PNC': 'Interest-based banking',

            # Insurance
            'BRK.A': 'Insurance/banking activities',
            'BRK.B': 'Insurance/banking activities',
            'AIG': 'Insurance',
            'PRU': 'Insurance',
            'MET': 'Insurance',

            # Payment Processing (interest-based)
            'V': 'Interest-based payment processing',
            'MA': 'Interest-based payment processing',
            'AXP': 'Credit/financial services',

            # Alcohol & Tobacco
            'BUD': 'Alcohol production',
            'TAP': 'Alcohol production',
            'DEO': 'Alcohol production',
            'MO': 'Tobacco',
            'PM': 'Tobacco',
            'BTI': 'Tobacco',

            # Gambling & Entertainment
            'LVS': 'Casino/gambling',
            'MGM': 'Casino/gambling',
            'WYNN': 'Casino/gambling',
            'CZR': 'Casino/gambling',

            # Defense/Weapons
            'LMT': 'Defense contractor',
            'RTX': 'Defense contractor',
            'NOC': 'Defense contractor',
            'GD': 'Defense contractor',
        }

        return {'halal': halal_stocks, 'haram': haram_stocks}
    
    def get_polygon_data(self, ticker: str) -> Optional[Dict]:
        """Get market cap and financial data from Polygon"""
        try:
            # Get ticker details for market cap
            url = f"https://api.polygon.io/v3/reference/tickers/{ticker}?apikey={self.polygon_key}"
            response = requests.get(url)

            if response.status_code != 200:
                return None

            data = response.json()
            if 'results' not in data:
                return None

            results = data['results']
            basic_data = {
                "ticker": ticker,
                "market_cap": results.get('market_cap'),
                "name": results.get('name'),
                "locale": results.get('locale', 'US'),
                "type": results.get('type')
            }

            # Get financial fundamentals for valuation
            financials_url = f"https://api.polygon.io/vX/reference/financials?ticker={ticker}&limit=1&apikey={self.polygon_key}"
            fin_response = requests.get(financials_url)

            if fin_response.status_code == 200:
                fin_data = fin_response.json()
                if 'results' in fin_data and fin_data['results']:
                    financials = fin_data['results'][0]

                    # Extract financial metrics
                    income_statement = financials.get('financials', {}).get('income_statement', {})
                    balance_sheet = financials.get('financials', {}).get('balance_sheet', {})

                    # Get key metrics
                    revenue = income_statement.get('revenues', {}).get('value', 0)
                    net_income = income_statement.get('net_income_loss', {}).get('value', 0)
                    shares_outstanding = income_statement.get('basic_average_shares', {}).get('value', 0)

                    # Calculate EPS and PE ratio
                    eps = net_income / shares_outstanding if shares_outstanding > 0 else 0
                    current_price = basic_data['market_cap'] / shares_outstanding if shares_outstanding > 0 else 0
                    pe_ratio = current_price / eps if eps > 0 else None

                    basic_data.update({
                        'revenue': revenue,
                        'net_income': net_income,
                        'eps': eps,
                        'pe_ratio': pe_ratio,
                        'shares_outstanding': shares_outstanding
                    })

            return basic_data

        except Exception as e:
            print(f"Error fetching Polygon data for {ticker}: {e}")
            return None

    def get_growth_estimates(self, ticker: str) -> Optional[float]:
        """Get growth estimates - simplified version using historical data"""
        try:
            # Get historical financials to estimate growth
            url = f"https://api.polygon.io/vX/reference/financials?ticker={ticker}&limit=5&apikey={self.polygon_key}"
            response = requests.get(url)

            if response.status_code != 200:
                return None

            data = response.json()
            if 'results' not in data or len(data['results']) < 2:
                return None

            # Calculate revenue growth from historical data
            revenues = []
            for result in data['results']:
                income_statement = result.get('financials', {}).get('income_statement', {})
                revenue = income_statement.get('revenues', {}).get('value')
                if revenue:
                    revenues.append(revenue)

            if len(revenues) >= 2:
                # Calculate average growth rate
                growth_rates = []
                for i in range(1, len(revenues)):
                    if revenues[i-1] > 0:
                        growth_rate = (revenues[i] - revenues[i-1]) / revenues[i-1]
                        growth_rates.append(growth_rate)

                if growth_rates:
                    avg_growth = sum(growth_rates) / len(growth_rates)
                    # Project 5-year growth (simplified)
                    return avg_growth

            return None

        except Exception as e:
            print(f"Error fetching growth data for {ticker}: {e}")
            return None

    def calculate_valuation_multiplier(self, stock_data: Dict) -> float:
        """Calculate valuation multiplier based on PE ratio and growth expectations"""

        pe_ratio = stock_data.get('pe_ratio')
        ticker = stock_data.get('ticker')

        # Get growth estimates
        growth_rate = self.get_growth_estimates(ticker)

        # Default growth rates by sector if we can't get data
        sector_growth_defaults = {
            'Technology': 0.15,  # 15% growth
            'Healthcare': 0.08,  # 8% growth
            'Consumer Goods': 0.05,  # 5% growth
            'E-commerce': 0.20,  # 20% growth
            'Automotive': 0.12,  # 12% growth (Tesla effect)
            'Media': 0.10,  # 10% growth
            'Retail': 0.06,  # 6% growth
            'Beverages': 0.04,  # 4% growth
        }

        # Use sector default if no growth data available
        if growth_rate is None:
            sector = stock_data.get('sector', 'Technology')
            growth_rate = sector_growth_defaults.get(sector, 0.08)  # 8% default

        # Calculate PEG ratio (PE / Growth rate in percentage)
        if pe_ratio and pe_ratio > 0 and growth_rate > 0:
            peg_ratio = pe_ratio / (growth_rate * 100)
        else:
            # Use industry average PE if missing
            sector_pe_defaults = {
                'Technology': 25,
                'Healthcare': 20,
                'Consumer Goods': 18,
                'E-commerce': 30,
                'Automotive': 22,
                'Media': 16,
                'Retail': 15,
                'Beverages': 20
            }
            sector = stock_data.get('sector', 'Technology')
            default_pe = sector_pe_defaults.get(sector, 22)
            peg_ratio = default_pe / (growth_rate * 100) if growth_rate > 0 else 1.5

        # Calculate valuation multiplier based on PEG ratio
        if peg_ratio < 0.8:
            multiplier = 1.4  # Highly undervalued
        elif peg_ratio < 1.0:
            multiplier = 1.3  # Undervalued
        elif peg_ratio < 1.2:
            multiplier = 1.1  # Slightly undervalued
        elif peg_ratio < 1.8:
            multiplier = 1.0  # Fair value
        elif peg_ratio < 2.5:
            multiplier = 0.9  # Slightly overvalued
        elif peg_ratio < 3.5:
            multiplier = 0.8  # Overvalued
        else:
            multiplier = 0.7  # Highly overvalued

        return {
            'multiplier': multiplier,
            'pe_ratio': pe_ratio,
            'growth_rate': growth_rate,
            'peg_ratio': peg_ratio,
            'valuation_category': self.get_valuation_category(peg_ratio)
        }

    def get_valuation_category(self, peg_ratio: float) -> str:
        """Get human-readable valuation category"""
        if peg_ratio < 0.8:
            return "Highly Undervalued"
        elif peg_ratio < 1.0:
            return "Undervalued"
        elif peg_ratio < 1.2:
            return "Slightly Undervalued"
        elif peg_ratio < 1.8:
            return "Fair Value"
        elif peg_ratio < 2.5:
            return "Slightly Overvalued"
        elif peg_ratio < 3.5:
            return "Overvalued"
        else:
            return "Highly Overvalued"
    
    def check_shariah_compliance(self, ticker: str, market_data: Dict) -> Dict:
        """Check if a stock is Shariah compliant using multiple criteria"""

        compliance_db = self.get_shariah_compliance_data()
        halal_stocks = compliance_db['halal']
        haram_stocks = compliance_db['haram']

        # Check if explicitly haram
        if ticker in haram_stocks:
            return {
                'is_halal': False,
                'confidence': 'high',
                'reason': haram_stocks[ticker],
                'source': 'manual_database'
            }

        # Check if explicitly halal
        if ticker in halal_stocks:
            stock_info = halal_stocks[ticker]
            return {
                'is_halal': True,
                'confidence': stock_info['confidence'],
                'reason': stock_info['notes'],
                'source': 'manual_database',
                'sector': stock_info['sector']
            }

        # For unknown stocks, apply basic screening
        # This is a simplified approach - in practice you'd need more detailed analysis
        return {
            'is_halal': False,  # Conservative approach for unknown stocks
            'confidence': 'low',
            'reason': 'Not in verified halal database',
            'source': 'conservative_screening'
        }

    def build_global_halal_portfolio(self, investment_amount: float = 1000) -> Optional[pd.DataFrame]:
        """Build portfolio from top global halal companies"""

        print("🌍 Building Global Halal ETF Portfolio...")
        print("=" * 60)

        # Step 1: Get top global companies
        print("1. Fetching top 100 global companies...")
        companies = self.get_top_global_companies()
        tickers = [c["ticker"] for c in companies]

        # Step 2: Get market cap data from Polygon
        print("2. Fetching market cap and fundamental data...")
        market_data = {}

        for i, ticker in enumerate(tickers[:50]):  # Limit to 50 for testing
            print(f"   Fetching {ticker} ({i+1}/50)...")
            polygon_data = self.get_polygon_data(ticker)
            if polygon_data and polygon_data.get('market_cap'):
                market_data[ticker] = polygon_data
            time.sleep(0.2)  # Rate limiting

        # Step 3: Apply Shariah compliance screening
        print("3. Applying Shariah compliance screening...")

        portfolio_data = []
        halal_count = 0
        haram_count = 0

        for ticker, market_info in market_data.items():
            compliance_result = self.check_shariah_compliance(ticker, market_info)

            print(f"   {ticker}: {'✅ HALAL' if compliance_result['is_halal'] else '❌ HARAM'} "
                  f"({compliance_result['confidence']} confidence) - {compliance_result['reason']}")

            if compliance_result['is_halal']:
                halal_count += 1

                # Calculate valuation metrics
                valuation_data = self.calculate_valuation_multiplier(market_info)

                portfolio_data.append({
                    'ticker': ticker,
                    'company_name': market_info.get('name', ''),
                    'market_cap': market_info['market_cap'],
                    'country': market_info.get('locale', 'US'),
                    'sector': compliance_result.get('sector', 'Unknown'),
                    'confidence': compliance_result['confidence'],
                    'halal_reason': compliance_result['reason'],
                    'compliance_source': compliance_result['source'],
                    'pe_ratio': valuation_data['pe_ratio'],
                    'growth_rate': valuation_data['growth_rate'],
                    'peg_ratio': valuation_data['peg_ratio'],
                    'valuation_multiplier': valuation_data['multiplier'],
                    'valuation_category': valuation_data['valuation_category']
                })
            else:
                haram_count += 1

        print(f"\n📊 Screening Results:")
        print(f"   ✅ Halal stocks: {halal_count}")
        print(f"   ❌ Haram stocks: {haram_count}")
        print(f"   📈 Portfolio stocks: {len(portfolio_data)}")

        if not portfolio_data:
            print("❌ No halal stocks found with valid market cap data")
            return None

        # Step 4: Create portfolio allocation with valuation adjustments
        df = pd.DataFrame(portfolio_data)

        print(f"\n📊 Valuation Analysis:")
        valuation_summary = df.groupby('valuation_category').size()
        for category, count in valuation_summary.items():
            print(f"   {category}: {count} stocks")

        # Calculate base market cap weights
        total_market_cap = df['market_cap'].sum()
        df['base_weight'] = df['market_cap'] / total_market_cap

        # Apply valuation-based adjustments (this is the key improvement!)
        df['adjusted_weight'] = df['base_weight'] * df['valuation_multiplier']

        # Redistribute excess weight to undervalued stocks
        total_adjusted = df['adjusted_weight'].sum()
        if total_adjusted != 1.0:
            undervalued_mask = df['valuation_multiplier'] > 1.0
            undervalued_stocks = df[undervalued_mask]

            if not undervalued_stocks.empty and total_adjusted < 1.0:
                # Distribute excess proportionally to undervalued stocks
                excess = 1.0 - total_adjusted
                undervalued_weights = undervalued_stocks['adjusted_weight'].sum()

                for idx in undervalued_stocks.index:
                    proportion = df.loc[idx, 'adjusted_weight'] / undervalued_weights
                    df.loc[idx, 'adjusted_weight'] += excess * proportion

        # Final normalization to ensure exactly 100%
        df['final_weight'] = df['adjusted_weight'] / df['adjusted_weight'].sum()
        df['allocation_eur'] = df['final_weight'] * investment_amount

        # Sort by allocation
        df = df.sort_values('allocation_eur', ascending=False)

        return df
    
    def generate_global_report(self, portfolio: pd.DataFrame, investment_amount: float):
        """Generate comprehensive global portfolio report"""

        print("\n" + "=" * 80)
        print("🌍 GLOBAL HALAL ETF ALLOCATION REPORT")
        print("=" * 80)
        print(f"Total Investment: €{investment_amount:,.2f}")
        print(f"Number of Holdings: {len(portfolio)}")
        print(f"Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Confidence distribution
        print(f"\n🔍 Confidence Distribution:")
        conf_dist = portfolio.groupby('confidence')['final_weight'].sum().sort_values(ascending=False)
        for confidence, weight in conf_dist.items():
            print(f"  {confidence.title()} confidence: {weight*100:.1f}%")

        # Sector distribution
        print(f"\n🏢 Sector Distribution:")
        sector_dist = portfolio.groupby('sector')['final_weight'].sum().sort_values(ascending=False)
        for sector, weight in sector_dist.items():
            print(f"  {sector}: {weight*100:.1f}%")

        # Geographic distribution
        print(f"\n📍 Geographic Distribution:")
        geo_dist = portfolio.groupby('country')['final_weight'].sum().sort_values(ascending=False)
        for country, weight in geo_dist.items():
            print(f"  {country}: {weight*100:.1f}%")

        # Valuation distribution
        print(f"\n📈 Valuation Distribution:")
        val_dist = portfolio.groupby('valuation_category')['final_weight'].sum().sort_values(ascending=False)
        for category, weight in val_dist.items():
            print(f"  {category}: {weight*100:.1f}%")

        print("\n" + "-" * 120)
        print(f"{'Ticker':<8} {'Company':<18} {'Sector':<10} {'PE':<6} {'PEG':<6} {'Val Mult':<9} {'Weight':<8} {'Amount €':<10}")
        print("-" * 120)

        for _, row in portfolio.head(20).iterrows():  # Top 20 holdings
            company_short = row['company_name'][:16] + ".." if len(row['company_name']) > 18 else row['company_name']
            sector_short = row['sector'][:8] + ".." if len(row['sector']) > 10 else row['sector']
            pe_str = f"{row['pe_ratio']:.1f}" if row['pe_ratio'] and row['pe_ratio'] > 0 else "N/A"
            peg_str = f"{row['peg_ratio']:.2f}" if row['peg_ratio'] else "N/A"

            print(f"{row['ticker']:<8} {company_short:<18} {sector_short:<10} "
                  f"{pe_str:<6} {peg_str:<6} {row['valuation_multiplier']:<9.2f} "
                  f"{row['final_weight']*100:>6.1f}% €{row['allocation_eur']:>8.0f}")

        if len(portfolio) > 20:
            others_weight = portfolio.iloc[20:]['final_weight'].sum()
            others_amount = portfolio.iloc[20:]['allocation_eur'].sum()
            print(f"{'Others':<8} {'('+str(len(portfolio)-20)+' more stocks)':<18} {'Mixed':<10} "
                  f"{'Mixed':<6} {'Mixed':<6} {'Mixed':<9} {others_weight*100:>6.1f}% €{others_amount:>8.0f}")

        print("-" * 120)
        print(f"{'TOTAL':<62} {'100.0%':<8} €{investment_amount:>8.0f}")

        # Summary statistics
        print(f"\n📈 Portfolio Statistics:")
        print(f"  Average allocation: €{portfolio['allocation_eur'].mean():.0f}")
        print(f"  Largest position: {portfolio.iloc[0]['ticker']} ({portfolio.iloc[0]['final_weight']*100:.1f}%)")
        print(f"  Top 5 concentration: {portfolio.head(5)['final_weight'].sum()*100:.1f}%")
        print(f"  High confidence stocks: {len(portfolio[portfolio['confidence'] == 'high'])}")
        print(f"  Technology exposure: {portfolio[portfolio['sector'] == 'Technology']['final_weight'].sum()*100:.1f}%")

        # Valuation statistics
        undervalued = portfolio[portfolio['valuation_multiplier'] > 1.0]
        overvalued = portfolio[portfolio['valuation_multiplier'] < 1.0]
        print(f"\n💰 Valuation Statistics:")
        print(f"  Undervalued stocks: {len(undervalued)} ({undervalued['final_weight'].sum()*100:.1f}% of portfolio)")
        print(f"  Overvalued stocks: {len(overvalued)} ({overvalued['final_weight'].sum()*100:.1f}% of portfolio)")
        print(f"  Average PE ratio: {portfolio['pe_ratio'].mean():.1f}" if portfolio['pe_ratio'].notna().any() else "  Average PE ratio: N/A")
        print(f"  Average PEG ratio: {portfolio['peg_ratio'].mean():.2f}" if portfolio['peg_ratio'].notna().any() else "  Average PEG ratio: N/A")
        print(f"  Average valuation multiplier: {portfolio['valuation_multiplier'].mean():.2f}")

        if len(undervalued) > 0:
            print(f"  Best value pick: {undervalued.iloc[0]['ticker']} (PEG: {undervalued.iloc[0]['peg_ratio']:.2f})")
        if len(overvalued) > 0:
            most_overvalued = overvalued.loc[overvalued['valuation_multiplier'].idxmin()]
            print(f"  Most expensive: {most_overvalued['ticker']} (PEG: {most_overvalued['peg_ratio']:.2f})")

def main():
    """Main execution function"""
    etf = GlobalHalalETF()
    
    # Build global halal portfolio
    portfolio = etf.build_global_halal_portfolio(investment_amount=1000)
    
    if portfolio is not None:
        etf.generate_global_report(portfolio, 1000)
        
        # Save results
        portfolio.to_csv('data/global_halal_portfolio.csv', index=False)
        print(f"\n💾 Portfolio saved to: data/global_halal_portfolio.csv")
    else:
        print("❌ Failed to build portfolio")

if __name__ == "__main__":
    main()
