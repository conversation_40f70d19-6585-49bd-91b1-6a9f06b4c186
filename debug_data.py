#!/usr/bin/env python3
"""
Debug script to check what data we're getting from Polygon API
"""

import requests
import json

API_KEY = "T590nL4IP66lUeIbYH8wDriLloGOcgxJ"
BASE_URL = "https://api.polygon.io"

def debug_single_stock(ticker):
    """Debug data for a single stock"""
    print(f"\n{'='*60}")
    print(f"DEBUGGING {ticker}")
    print(f"{'='*60}")
    
    # 1. Get ticker details
    print("1. Getting ticker details...")
    url = f"{BASE_URL}/v3/reference/tickers/{ticker}?apikey={API_KEY}"
    response = requests.get(url)
    ticker_data = response.json()
    
    print(f"Status: {response.status_code}")
    if 'results' in ticker_data:
        results = ticker_data['results']
        print(f"Market Cap: {results.get('market_cap', 'N/A')}")
        print(f"Name: {results.get('name', 'N/A')}")
        print(f"Type: {results.get('type', 'N/A')}")
    else:
        print("No results in ticker data")
        print(f"Response: {ticker_data}")
    
    # 2. Get financials
    print("\n2. Getting financials...")
    url = f"{BASE_URL}/vX/reference/financials?ticker={ticker}&limit=1&apikey={API_KEY}"
    response = requests.get(url)
    financial_data = response.json()
    
    print(f"Status: {response.status_code}")
    if 'results' in financial_data and financial_data['results']:
        financials = financial_data['results'][0]
        print(f"Filing date: {financials.get('filing_date', 'N/A')}")
        print(f"Period: {financials.get('timeframe', 'N/A')}")
        
        # Check balance sheet
        balance_sheet = financials.get('financials', {}).get('balance_sheet', {})
        if balance_sheet:
            print("\nBalance Sheet Data:")
            current_liab = balance_sheet.get('current_liabilities', {}).get('value', 0)
            noncurrent_liab = balance_sheet.get('noncurrent_liabilities', {}).get('value', 0)
            equity = balance_sheet.get('equity', {}).get('value', 0)
            
            print(f"  Current Liabilities: {current_liab}")
            print(f"  Non-current Liabilities: {noncurrent_liab}")
            print(f"  Total Debt: {current_liab + noncurrent_liab}")
            print(f"  Equity: {equity}")
            
            if equity > 0:
                debt_equity = (current_liab + noncurrent_liab) / equity
                print(f"  Debt-to-Equity Ratio: {debt_equity:.3f}")
                print(f"  Passes 30% test: {debt_equity <= 0.30}")
            else:
                print("  Cannot calculate D/E ratio - zero equity")
        else:
            print("No balance sheet data available")
            
        # Check income statement
        income_statement = financials.get('financials', {}).get('income_statement', {})
        if income_statement:
            print("\nIncome Statement Data:")
            revenue = income_statement.get('revenues', {}).get('value', 'N/A')
            print(f"  Revenue: {revenue}")
        else:
            print("No income statement data available")
            
    else:
        print("No financial results")
        print(f"Response: {financial_data}")
    
    # 3. Check if in halal list
    halal_tickers = {
        'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'TSM',
        'ASML', 'AVGO', 'ORCL', 'CRM', 'ADBE', 'NFLX', 'AMD', 'INTC',
        'QCOM', 'TXN', 'INTU', 'CSCO', 'IBM', 'UBER', 'SHOP', 'SQ',
        'PYPL', 'ZOOM', 'DOCU', 'OKTA', 'SNOW', 'PLTR', 'RBLX', 'COIN'
    }
    
    print(f"\n3. Halal List Check:")
    print(f"  In halal list: {ticker in halal_tickers}")

def debug_multiple_stocks():
    """Debug multiple stocks to see patterns"""
    stocks_to_test = ["AAPL", "MSFT", "GOOGL", "ORCL", "ADBE", "AMD", "SHOP"]
    
    print("SUMMARY OF ALL STOCKS:")
    print("="*80)
    
    for ticker in stocks_to_test:
        debug_single_stock(ticker)
        print("\n" + "-"*60)

if __name__ == "__main__":
    debug_multiple_stocks()
