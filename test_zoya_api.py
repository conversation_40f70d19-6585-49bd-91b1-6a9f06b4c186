#!/usr/bin/env python3
"""
Test script to check Zoya API connectivity and response format
"""

import requests
import json

# Zoya API Configuration
ZOYA_SANDBOX_ENDPOINT = "https://sandbox-api.zoya.finance/graphql"
ZOYA_LIVE_ENDPOINT = "https://api.zoya.finance/graphql"
ZOYA_SANDBOX_KEY = "sandbox-3b0ba3bb-fa32-4d0d-bc94-3bc5052c36de"

def test_zoya_api():
    """Test different Zoya API configurations"""
    
    test_tickers = ["AAPL", "MSFT", "GOOGL", "JPM", "BAC"]  # Mix of halal and haram
    
    # Test 1: Simple query without authentication
    print("🧪 Testing Zoya API...")
    print("=" * 60)
    
    # GraphQL query
    query = """
    query GetStockCompliance($tickers: [String!]!) {
        stocks(tickers: $tickers) {
            ticker
            complianceStatus
            complianceScore
            complianceReasons
            lastUpdated
            company {
                name
                sector
                country
            }
        }
    }
    """
    
    variables = {"tickers": test_tickers}
    payload = {"query": query, "variables": variables}
    
    # Test configurations
    test_configs = [
        {
            "name": "Sandbox with API Key",
            "endpoint": ZOYA_SANDBOX_ENDPOINT,
            "headers": {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {ZOYA_SANDBOX_KEY}"
            }
        },
        {
            "name": "Sandbox without API Key",
            "endpoint": ZOYA_SANDBOX_ENDPOINT,
            "headers": {"Content-Type": "application/json"}
        },
        {
            "name": "Live without API Key",
            "endpoint": ZOYA_LIVE_ENDPOINT,
            "headers": {"Content-Type": "application/json"}
        }
    ]
    
    for config in test_configs:
        print(f"\n📡 Testing: {config['name']}")
        print(f"Endpoint: {config['endpoint']}")
        
        try:
            response = requests.post(
                config['endpoint'],
                json=payload,
                headers=config['headers'],
                timeout=10
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Success!")
                
                if 'data' in data and data['data'] and 'stocks' in data['data']:
                    stocks = data['data']['stocks']
                    print(f"Found {len(stocks)} stocks:")
                    
                    for stock in stocks:
                        status = stock.get('complianceStatus', 'UNKNOWN')
                        company = stock.get('company', {})
                        name = company.get('name', 'Unknown')
                        print(f"  {stock['ticker']}: {status} - {name}")
                        
                elif 'errors' in data:
                    print("❌ GraphQL Errors:")
                    for error in data['errors']:
                        print(f"  - {error.get('message', 'Unknown error')}")
                else:
                    print("⚠️  Unexpected response format:")
                    print(json.dumps(data, indent=2)[:500] + "...")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text[:200]}...")
                
        except requests.exceptions.Timeout:
            print("⏰ Request timed out")
        except requests.exceptions.ConnectionError:
            print("🔌 Connection error")
        except Exception as e:
            print(f"💥 Error: {e}")
        
        print("-" * 40)

def test_simple_rest_api():
    """Test if Zoya has a simpler REST API"""
    print("\n🔍 Testing potential REST endpoints...")
    
    rest_endpoints = [
        "https://api.zoya.finance/stocks/AAPL",
        "https://api.zoya.finance/v1/stocks/AAPL",
        "https://api.zoya.finance/compliance/AAPL",
        "https://sandbox-api.zoya.finance/stocks/AAPL",
        "https://sandbox-api.zoya.finance/v1/stocks/AAPL"
    ]
    
    for endpoint in rest_endpoints:
        try:
            response = requests.get(endpoint, timeout=5)
            print(f"{endpoint}: {response.status_code}")
            if response.status_code == 200:
                print(f"  ✅ Success: {response.text[:100]}...")
        except:
            print(f"{endpoint}: ❌ Failed")

def create_fallback_halal_list():
    """Create a comprehensive fallback halal stock list"""
    print("\n📋 Creating fallback halal stock database...")
    
    # Based on common Islamic finance screening
    halal_stocks = {
        # Technology (generally halal if debt ratios are acceptable)
        'AAPL': {'name': 'Apple Inc.', 'sector': 'Technology', 'confidence': 'high'},
        'MSFT': {'name': 'Microsoft Corp', 'sector': 'Technology', 'confidence': 'high'},
        'GOOGL': {'name': 'Alphabet Inc.', 'sector': 'Technology', 'confidence': 'medium'},
        'AMZN': {'name': 'Amazon.com Inc.', 'sector': 'Technology', 'confidence': 'medium'},
        'NVDA': {'name': 'NVIDIA Corp', 'sector': 'Technology', 'confidence': 'high'},
        'META': {'name': 'Meta Platforms Inc.', 'sector': 'Technology', 'confidence': 'medium'},
        'TSLA': {'name': 'Tesla Inc.', 'sector': 'Automotive', 'confidence': 'high'},
        'NFLX': {'name': 'Netflix Inc.', 'sector': 'Media', 'confidence': 'medium'},
        'AMD': {'name': 'Advanced Micro Devices', 'sector': 'Technology', 'confidence': 'high'},
        'INTC': {'name': 'Intel Corp', 'sector': 'Technology', 'confidence': 'high'},
        'ORCL': {'name': 'Oracle Corp', 'sector': 'Technology', 'confidence': 'high'},
        'CRM': {'name': 'Salesforce Inc.', 'sector': 'Technology', 'confidence': 'high'},
        'SHOP': {'name': 'Shopify Inc.', 'sector': 'Technology', 'confidence': 'high'},
        
        # Healthcare & Pharmaceuticals
        'JNJ': {'name': 'Johnson & Johnson', 'sector': 'Healthcare', 'confidence': 'medium'},
        'PFE': {'name': 'Pfizer Inc.', 'sector': 'Healthcare', 'confidence': 'medium'},
        'UNH': {'name': 'UnitedHealth Group', 'sector': 'Healthcare', 'confidence': 'low'},
        
        # Consumer Goods
        'PG': {'name': 'Procter & Gamble', 'sector': 'Consumer Goods', 'confidence': 'high'},
        'KO': {'name': 'Coca-Cola Co.', 'sector': 'Beverages', 'confidence': 'high'},
        'NKE': {'name': 'Nike Inc.', 'sector': 'Consumer Goods', 'confidence': 'high'},
        
        # International
        'TSM': {'name': 'Taiwan Semiconductor', 'sector': 'Technology', 'confidence': 'high'},
        'ASML': {'name': 'ASML Holding NV', 'sector': 'Technology', 'confidence': 'high'},
        'SAP': {'name': 'SAP SE', 'sector': 'Technology', 'confidence': 'high'},
    }
    
    # Definitely haram stocks (for reference)
    haram_stocks = {
        'JPM': 'Banking/Interest',
        'BAC': 'Banking/Interest', 
        'WFC': 'Banking/Interest',
        'GS': 'Banking/Interest',
        'MS': 'Banking/Interest',
        'AIG': 'Insurance',
        'BRK.A': 'Insurance/Banking',
        'V': 'Interest-based payments',
        'MA': 'Interest-based payments',
    }
    
    print(f"✅ Halal stocks database: {len(halal_stocks)} stocks")
    print(f"❌ Haram stocks database: {len(haram_stocks)} stocks")
    
    return halal_stocks, haram_stocks

if __name__ == "__main__":
    test_zoya_api()
    test_simple_rest_api()
    create_fallback_halal_list()
